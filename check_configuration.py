#!/usr/bin/env python3
"""
Configuration Checker for Project Chimera
Checks what API keys and environment variables are missing or need attention.
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, <PERSON><PERSON>

def check_environment_variables() -> Dict[str, Dict]:
    """Check all required environment variables"""
    
    # Define all required variables with their importance and status
    variables = {
        # Critical Infrastructure
        "DATABASE_URL": {
            "required": True,
            "description": "PostgreSQL database connection",
            "example": "postgresql://user:password@localhost:5432/chimera_db",
            "cost": "Free (local) or $7/month (Render)"
        },
        "REDIS_URL": {
            "required": True,
            "description": "Redis pub/sub messaging",
            "example": "redis://localhost:6379",
            "cost": "Free (local) or $7/month (Render)"
        },
        
        # Blockchain Access
        "INFURA_API_KEY": {
            "required": True,
            "description": "Ethereum node access via Infura",
            "example": "********************************",
            "cost": "Free tier (100k requests/day)",
            "get_url": "https://infura.io"
        },
        "PRIVATE_KEY_PATH": {
            "required": True,
            "description": "Path to wallet private key file",
            "example": "/etc/secrets/trader-pk",
            "cost": "Free (your wallet)",
            "security": "🔒 CRITICAL - Never commit to git"
        },
        
        # Data Sources (Paid)
        "TOKENUNLOCKS_API_KEY": {
            "required": False,
            "description": "Primary unlock data source (DEPRECATED - tokenunlocks.com is for sale)",
            "example": "your_tokenunlocks_api_key",
            "cost": "N/A - Service no longer available",
            "get_url": "DEPRECATED - Domain for sale",
            "status": "DEPRECATED"
        },
        "VESTLAB_API_KEY": {
            "required": False,
            "description": "Secondary vesting data source (DEPRECATED - vestlab.io is parked)",
            "example": "your_vestlab_api_key",
            "cost": "N/A - Service no longer available",
            "get_url": "DEPRECATED - Domain parked",
            "status": "DEPRECATED"
        },
        
        # Trading Parameters
        "PRESSURE_SCORE_THRESHOLD": {
            "required": False,
            "description": "Minimum score to trigger trades",
            "example": "0.75",
            "cost": "Free (configuration)"
        },
        "STOP_LOSS_PCT": {
            "required": False,
            "description": "Stop loss percentage",
            "example": "0.15",
            "cost": "Free (configuration)"
        },
        "TAKE_PROFIT_PCT": {
            "required": False,
            "description": "Take profit percentage",
            "example": "0.10",
            "cost": "Free (configuration)"
        },
        "BORROW_AMOUNT_PER_TRADE": {
            "required": False,
            "description": "Amount to borrow per trade",
            "example": "1000",
            "cost": "Free (configuration)"
        },
        "PAPER_TRADING_MODE": {
            "required": False,
            "description": "Enable paper trading (recommended for testing)",
            "example": "true",
            "cost": "Free"
        },
        
        # Working Data Sources (2025 Update)
        "COINGECKO_API_KEY": {
            "required": False,
            "description": "Market data & token analytics (free tier: 50 calls/min)",
            "example": "your_coingecko_api_key",
            "cost": "Free tier available",
            "get_url": "https://coingecko.com/api"
        },
        "DEXSCREENER_API_KEY": {
            "required": False,
            "description": "Real-time DEX analytics (proven reliable)",
            "example": "your_dexscreener_api_key",
            "cost": "Free tier available",
            "get_url": "https://dexscreener.com/api"
        },
        "DEXTOOLS_API_KEY": {
            "required": False,
            "description": "DEXTools analytics - token scoring, pool data, trending (actively maintained)",
            "example": "your_dextools_api_key",
            "cost": "Free tier available, Pro plans from $29/month",
            "get_url": "https://developer.dextools.io"
        },
        "THEGRAPH_API_KEY": {
            "required": False,
            "description": "On-chain data indexing (industry standard)",
            "example": "your_thegraph_api_key",
            "cost": "Free tier available",
            "get_url": "https://thegraph.com"
        },
        "NANSEN_API_KEY": {
            "required": False,
            "description": "Whale tracking & on-chain flows (premium)",
            "example": "your_nansen_api_key",
            "cost": "$150+/month",
            "get_url": "https://nansen.ai"
        },
        "GLASSNODE_API_KEY": {
            "required": False,
            "description": "On-chain metrics & analytics (premium)",
            "example": "your_glassnode_api_key",
            "cost": "$29+/month",
            "get_url": "https://glassnode.com"
        },

        # Notifications
        "TELEGRAM_BOT_TOKEN": {
            "required": False,
            "description": "Telegram bot for notifications",
            "example": "123456:ABC-DEF...",
            "cost": "Free",
            "get_url": "https://t.me/BotFather"
        },
        "TELEGRAM_CHAT_ID": {
            "required": False,
            "description": "Your Telegram chat ID",
            "example": "123456789",
            "cost": "Free"
        },

        # Trading Infrastructure
        "ONEINCH_API_KEY": {
            "required": False,
            "description": "DEX aggregation for optimal swaps",
            "example": "your_1inch_api_key",
            "cost": "Free tier available",
            "get_url": "https://1inch.io/api"
        }
    }
    
    results = {}
    
    for var_name, config in variables.items():
        value = os.environ.get(var_name)
        
        results[var_name] = {
            "config": config,
            "value": value,
            "status": "SET" if value else "MISSING",
            "is_default": False
        }
        
        # Check if it's a default/example value that needs to be changed
        if value and "example" in config:
            if value == config["example"]:
                results[var_name]["status"] = "DEFAULT"
                results[var_name]["is_default"] = True
    
    return results

def check_file_dependencies() -> List[Dict]:
    """Check if required files exist"""
    files_to_check = [
        {
            "path": ".env",
            "description": "Environment variables file",
            "required": False,
            "create_command": "cp .env.example .env"
        },
        {
            "path": "requirements.txt",
            "description": "Python dependencies",
            "required": True
        },
        {
            "path": "render.yaml",
            "description": "Deployment configuration",
            "required": True
        }
    ]
    
    results = []
    for file_info in files_to_check:
        exists = Path(file_info["path"]).exists()
        results.append({
            **file_info,
            "exists": exists,
            "status": "EXISTS" if exists else "MISSING"
        })
    
    return results

def print_configuration_report():
    """Print a comprehensive configuration report"""
    
    print("🔍 Project Chimera - Configuration Status Report")
    print("=" * 60)
    
    # Check environment variables
    env_vars = check_environment_variables()
    
    # Categorize variables
    critical_missing = []
    critical_default = []
    optional_missing = []
    configured = []
    
    for var_name, info in env_vars.items():
        if info["config"]["required"]:
            if info["status"] == "MISSING":
                critical_missing.append((var_name, info))
            elif info["status"] == "DEFAULT":
                critical_default.append((var_name, info))
            else:
                configured.append((var_name, info))
        else:
            if info["status"] == "MISSING":
                optional_missing.append((var_name, info))
            else:
                configured.append((var_name, info))
    
    # Print critical issues
    if critical_missing:
        print("\n🚨 CRITICAL - Missing Required Variables:")
        for var_name, info in critical_missing:
            config = info["config"]
            print(f"   ❌ {var_name}")
            print(f"      Description: {config['description']}")
            print(f"      Cost: {config['cost']}")
            if "get_url" in config:
                print(f"      Get it at: {config['get_url']}")
            print()
    
    if critical_default:
        print("\n⚠️  CRITICAL - Using Default/Example Values:")
        for var_name, info in critical_default:
            config = info["config"]
            print(f"   🔄 {var_name}")
            print(f"      Current: {info['value']}")
            print(f"      Description: {config['description']}")
            if "security" in config:
                print(f"      Security: {config['security']}")
            print()
    
    # Print configured items
    if configured:
        print(f"\n✅ Configured Variables ({len(configured)}):")
        for var_name, info in configured:
            status_icon = "🔒" if "security" in info["config"] else "✅"
            print(f"   {status_icon} {var_name}")
    
    # Print optional missing
    if optional_missing:
        print(f"\n📋 Optional Variables (can be added later):")
        for var_name, info in optional_missing:
            config = info["config"]
            print(f"   ⭕ {var_name} - {config['description']}")
    
    # Check files
    print("\n📁 File Dependencies:")
    files = check_file_dependencies()
    for file_info in files:
        status_icon = "✅" if file_info["exists"] else "❌"
        print(f"   {status_icon} {file_info['path']} - {file_info['description']}")
        if not file_info["exists"] and "create_command" in file_info:
            print(f"      Create with: {file_info['create_command']}")
    
    # Summary and next steps
    print("\n🎯 Next Steps:")
    
    if critical_missing or critical_default:
        print("   1. 🔑 Set up critical API keys and infrastructure")
        print("   2. 💾 Create local .env file: cp .env.example .env")
        print("   3. 🗄️  Set up PostgreSQL and Redis (local or cloud)")
        print("   4. 🧪 Run tests: python -m pytest tests/ -v")
    else:
        print("   ✅ All critical configuration is set!")
        print("   🧪 Run tests: python -m pytest tests/ -v")
        print("   🚀 Ready for deployment!")
    
    # Cost summary
    total_required_cost = 0
    print(f"\n💰 Cost Summary:")
    print(f"   Free tier testing: $0/month")
    print(f"   Production ready: ~$175/month")
    print(f"   (TokenUnlocks $75 + Vestlab $50 + Infura Pro $50)")

if __name__ == "__main__":
    print_configuration_report()
