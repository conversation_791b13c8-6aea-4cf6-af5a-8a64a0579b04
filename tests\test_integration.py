#!/usr/bin/env python3
"""
Integration tests for Project Chimera
Tests the complete workflow from unlock detection to trade execution
"""

import unittest
import sys
import os
import json
import time
from unittest.mock import patch, MagicMock
from decimal import Decimal

class TestChimeraIntegration(unittest.TestCase):
    """Test the complete Chimera workflow"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_unlock_event = {
            'token_symbol': 'TESTTOKEN',
            'contract_address': '0x1234567890123456789012345678901234567890',
            'unlock_date': '2024-12-01T00:00:00Z',
            'unlock_amount': 100000000,
            'circulating_supply': 200000000,
            'total_supply': 1000000000,
            'source': 'test'
        }
        
        self.test_trade_candidate = {
            'token_symbol': 'TESTTOKEN',
            'contract_address': '0x1234567890123456789012345678901234567890',
            'unlock_date': '2024-12-01T00:00:00Z',
            'strategy_id': 'pre_unlock_decay_v1',
            'pressure_score': 2.5,
            'unlock_amount': 100000000,
            'circulating_supply': 200000000
        }
        
        self.test_position = {
            'position_id': 1,
            'token_symbol': 'TESTTOKEN',
            'token_address': '0x1234567890123456789012345678901234567890',
            'amount_shorted': 1000,
            'entry_price_in_usdc': Decimal('2.00'),
            'unlock_date': '2024-12-01T00:00:00Z',
            'status': 'OPEN'
        }
    
    def test_oracle_to_seer_workflow(self):
        """Test Oracle -> Seer workflow"""
        # Add paths for testing
        oracle_path = os.path.join(os.path.dirname(__file__), '..', 'services', 'the-oracle')
        seer_path = os.path.join(os.path.dirname(__file__), '..', 'services', 'the-seer')
        sys.path.insert(0, oracle_path)
        sys.path.insert(0, seer_path)
        
        from data_sources import fetch_token_unlocks_data
        from analysis import calculate_unlock_pressure_score
        from onchain_checker import is_token_borrowable
        
        # 1. Oracle fetches data
        unlock_events = fetch_token_unlocks_data()
        self.assertIsInstance(unlock_events, list)
        self.assertGreater(len(unlock_events), 0)
        
        # 2. Seer analyzes the first event
        event = unlock_events[0]
        pressure_score = calculate_unlock_pressure_score(event)
        self.assertIsInstance(pressure_score, float)
        self.assertGreaterEqual(pressure_score, 0)
        
        # 3. Check if token is borrowable (mock this for testing)
        with patch('onchain_checker.is_token_borrowable', return_value=True):
            is_borrowable = is_token_borrowable(event['contract_address'])
            self.assertTrue(is_borrowable)
    
    def test_seer_to_executioner_workflow(self):
        """Test Seer -> Executioner workflow"""
        seer_path = os.path.join(os.path.dirname(__file__), '..', 'services', 'the-seer')
        executioner_path = os.path.join(os.path.dirname(__file__), '..', 'services', 'the-executioner')
        sys.path.insert(0, seer_path)
        sys.path.insert(0, executioner_path)
        
        from analysis import calculate_unlock_pressure_score
        
        # 1. Seer creates trade candidate
        pressure_score = calculate_unlock_pressure_score(self.test_unlock_event)
        
        if pressure_score > 0.75:  # Above threshold
            trade_candidate = {
                **self.test_unlock_event,
                'strategy_id': 'pre_unlock_decay_v1',
                'pressure_score': pressure_score
            }
            
            # 2. Executioner would receive this candidate
            # (In real system, this would be via Redis pub/sub)
            self.assertIn('token_symbol', trade_candidate)
            self.assertIn('contract_address', trade_candidate)
            self.assertIn('pressure_score', trade_candidate)
    
    def test_executioner_to_ledger_workflow(self):
        """Test Executioner -> Ledger workflow"""
        ledger_path = os.path.join(os.path.dirname(__file__), '..', 'services', 'the-ledger')
        sys.path.insert(0, ledger_path)
        
        from risk_manager import check_risk_rules
        
        # 1. Executioner creates position (simulated)
        position = self.test_position.copy()
        
        # 2. Ledger monitors position
        current_price = Decimal('1.90')  # 5% profit for short
        action, reason = check_risk_rules(position, current_price)
        
        self.assertIn(action, ['HOLD', 'CLOSE'])
        self.assertIsInstance(reason, str)
    
    def test_risk_management_scenarios(self):
        """Test various risk management scenarios"""
        ledger_path = os.path.join(os.path.dirname(__file__), '..', 'services', 'the-ledger')
        sys.path.insert(0, ledger_path)
        
        from risk_manager import check_risk_rules
        
        position = self.test_position.copy()
        
        # Scenario 1: Stop loss trigger
        stop_loss_price = Decimal('2.30')  # 15% loss
        action, reason = check_risk_rules(position, stop_loss_price)
        self.assertEqual(action, "CLOSE")
        self.assertIn("Stop-Loss", reason)
        
        # Scenario 2: Take profit trigger
        take_profit_price = Decimal('1.80')  # 10% profit
        action, reason = check_risk_rules(position, take_profit_price)
        self.assertEqual(action, "CLOSE")
        self.assertIn("Take-Profit", reason)
        
        # Scenario 3: Normal monitoring
        normal_price = Decimal('1.95')  # Small profit
        action, reason = check_risk_rules(position, normal_price)
        self.assertEqual(action, "HOLD")
    
    def test_notification_formatting(self):
        """Test Herald notification formatting"""
        herald_path = os.path.join(os.path.dirname(__file__), '..', 'services', 'the-herald')
        sys.path.insert(0, herald_path)
        
        from main import format_message
        
        # Test trade candidate notification
        candidate_msg = format_message("chimera:trade_candidates", self.test_trade_candidate)
        self.assertIn("Trade Candidate", candidate_msg)
        self.assertIn("TESTTOKEN", candidate_msg)
        self.assertIn("2.5", candidate_msg)  # Pressure score
        
        # Test position opened notification
        position_msg = format_message("chimera:position_opened", {
            'token_symbol': 'TESTTOKEN',
            'position_id': 1,
            'amount_shorted': '1000',
            'entry_price_in_usdc': '2.00'
        })
        self.assertIn("Position Opened", position_msg)
        self.assertIn("TESTTOKEN", position_msg)
        self.assertIn("1000", position_msg)
        
        # Test close position notification
        close_msg = format_message("chimera:close_position", {
            'position_id': 1,
            'token_symbol': 'TESTTOKEN',
            'reason': 'Stop-Loss triggered',
            'current_price': '2.30',
            'entry_price': '2.00'
        })
        self.assertIn("Closing Position", close_msg)
        self.assertIn("Stop-Loss", close_msg)
    
    @patch('redis.from_url')
    def test_redis_communication(self, mock_redis):
        """Test Redis pub/sub communication between services"""
        mock_redis_instance = MagicMock()
        mock_redis.return_value = mock_redis_instance
        
        # Test publishing an unlock event
        oracle_path = os.path.join(os.path.dirname(__file__), '..', 'services', 'the-oracle')
        sys.path.insert(0, oracle_path)
        
        from event_publisher import publish_unlock_event
        
        publish_unlock_event(self.test_unlock_event)
        
        # Verify Redis publish was called
        mock_redis_instance.publish.assert_called()
        
        # Check the call arguments
        call_args = mock_redis_instance.publish.call_args
        channel = call_args[0][0]
        message = call_args[0][1]
        
        self.assertEqual(channel, "chimera:unlock_events")
        
        # Parse the message
        message_data = json.loads(message)
        self.assertEqual(message_data['token_symbol'], 'TESTTOKEN')
    
    def test_database_operations(self):
        """Test database operations (requires DATABASE_URL)"""
        if not os.environ.get("DATABASE_URL"):
            self.skipTest("DATABASE_URL not set, skipping database tests")
        
        oracle_path = os.path.join(os.path.dirname(__file__), '..', 'services', 'the-oracle')
        sys.path.insert(0, oracle_path)
        
        from db_handler import store_unlock_events, get_upcoming_unlocks
        
        # Test storing events
        test_events = [self.test_unlock_event]
        
        try:
            store_unlock_events(test_events)
            
            # Test retrieving events
            upcoming = get_upcoming_unlocks(days_ahead=30)
            self.assertIsInstance(upcoming, list)
            
        except Exception as e:
            self.skipTest(f"Database operation failed: {e}")
    
    def test_end_to_end_simulation(self):
        """Simulate the complete end-to-end workflow"""
        # This test simulates the entire workflow without actual external dependencies
        
        # 1. Oracle phase - data ingestion
        unlock_events = [self.test_unlock_event]
        self.assertEqual(len(unlock_events), 1)
        
        # 2. Seer phase - analysis
        seer_path = os.path.join(os.path.dirname(__file__), '..', 'services', 'the-seer')
        sys.path.insert(0, seer_path)
        
        from analysis import calculate_unlock_pressure_score
        
        event = unlock_events[0]
        pressure_score = calculate_unlock_pressure_score(event)
        
        # Should generate a high score for this test event
        self.assertGreater(pressure_score, 1.0)
        
        # 3. Executioner phase - trade execution (simulated)
        if pressure_score > 0.75:
            trade_executed = True
            position_created = {
                **self.test_position,
                'pressure_score': pressure_score
            }
        else:
            trade_executed = False
            position_created = None
        
        self.assertTrue(trade_executed)
        self.assertIsNotNone(position_created)
        
        # 4. Ledger phase - risk monitoring
        if position_created:
            ledger_path = os.path.join(os.path.dirname(__file__), '..', 'services', 'the-ledger')
            sys.path.insert(0, ledger_path)
            
            from risk_manager import check_risk_rules
            
            # Simulate price movement
            current_price = Decimal('1.90')  # 5% profit
            action, reason = check_risk_rules(position_created, current_price)
            
            self.assertEqual(action, "HOLD")  # Should hold with small profit
        
        # 5. Herald phase - notifications (simulated)
        notifications_sent = [
            "Trade candidate identified",
            "Position opened",
            "Risk check completed"
        ]
        
        self.assertEqual(len(notifications_sent), 3)

if __name__ == '__main__':
    # Set up test environment
    os.environ.setdefault('PRESSURE_SCORE_THRESHOLD', '0.75')
    os.environ.setdefault('STOP_LOSS_PCT', '0.15')
    os.environ.setdefault('TAKE_PROFIT_PCT', '0.10')
    
    unittest.main()
