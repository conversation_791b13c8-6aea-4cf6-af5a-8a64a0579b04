import requests
import logging
from typing import Optional, Dict, Any

# Updated Aave V3 Subgraph URLs (from official Aave repository)
# Using the decentralized network endpoint for ETH Mainnet V3
# From: https://thegraph.com/explorer/subgraphs/Cd2gEDVeqnjBn1hSeqFMitw8Q1iiyV9FYUZkLNRcL87g
AAVE_V3_SUBGRAPH_URL = "https://gateway-arbitrum.network.thegraph.com/api/subgraphs/id/Cd2gEDVeqnjBn1hSeqFMitw8Q1iiyV9FYUZkLNRcL87g"

# Backup endpoint - try the hosted service format
AAVE_V3_BACKUP_URL = "https://api.thegraph.com/subgraphs/name/aave/protocol-v3-ethereum"

# Compound V3 - keeping original for now
COMPOUND_V3_SUBGRAPH_URL = "https://api.thegraph.com/subgraphs/name/graphprotocol/compound-v3"

def is_token_borrowable(contract_address: str) -> bool:
    """
    Checks if a token is available for borrowing on major lending protocols.
    Returns True if the token is listed and borrowing is enabled on any protocol.

    Uses a multi-tier approach:
    1. Try Aave V3 API check
    2. Try Compound V3 API check
    3. Fallback to major token whitelist
    4. Default to False for unknown tokens
    """
    if not contract_address:
        return False

    # First, check if it's a major token (fast whitelist check)
    if is_major_token(contract_address):
        logging.info(f"Token {contract_address} is a major token, assuming borrowable")
        return True

    # Try API checks for non-major tokens
    try:
        # Check Aave V3
        if is_borrowable_on_aave(contract_address):
            logging.info(f"Token {contract_address} is borrowable on Aave V3")
            return True
    except Exception as e:
        logging.warning(f"Aave check failed for {contract_address}: {e}")

    try:
        # Check Compound V3 (if Aave fails)
        if is_borrowable_on_compound(contract_address):
            logging.info(f"Token {contract_address} is borrowable on Compound V3")
            return True
    except Exception as e:
        logging.warning(f"Compound check failed for {contract_address}: {e}")

    logging.info(f"Token {contract_address} is not borrowable on checked protocols")
    return False

def is_borrowable_on_aave(contract_address: str) -> bool:
    """
    Checks if a token is available for borrowing on Aave V3 by querying its subgraph.
    Returns True if the token is listed and borrowing is enabled.
    """
    # Updated GraphQL query for Aave V3 - reserve ID is just the token address
    query = f"""
    {{
        reserve(id: "{contract_address.lower()}") {{
            id
            borrowingEnabled
            isActive
            isFrozen
            symbol
            name
        }}
    }}
    """

    try:
        # Try primary endpoint first
        response = requests.post(
            AAVE_V3_SUBGRAPH_URL,
            json={'query': query},
            timeout=10
        )
        response.raise_for_status()
        data = response.json()

        if 'errors' in data:
            logging.warning(f"GraphQL errors in Aave query: {data['errors']}")
            # Try backup endpoint if primary fails
            return _try_aave_backup_endpoint(contract_address, query)

        reserve_data = data.get("data", {}).get("reserve")
        if reserve_data:
            is_borrowable = (
                reserve_data.get("borrowingEnabled") is True and
                reserve_data.get("isActive") is True and
                reserve_data.get("isFrozen") is False
            )
            logging.info(f"Aave reserve data for {contract_address}: {reserve_data}")
            return is_borrowable
        else:
            logging.info(f"No reserve found for {contract_address} on Aave V3")
            return False

    except requests.RequestException as e:
        logging.warning(f"Error querying Aave subgraph: {e}")
        # Try backup endpoint
        return _try_aave_backup_endpoint(contract_address, query)
    except Exception as e:
        logging.error(f"Unexpected error checking Aave: {e}")

    return False

def _try_aave_backup_endpoint(contract_address: str, query: str) -> bool:
    """Try the backup Aave endpoint if primary fails"""
    try:
        logging.info("Trying Aave backup endpoint...")
        response = requests.post(
            AAVE_V3_BACKUP_URL,
            json={'query': query},
            timeout=10
        )
        response.raise_for_status()
        data = response.json()

        if 'errors' in data:
            logging.warning(f"Backup endpoint also failed: {data['errors']}")
            return False

        reserve_data = data.get("data", {}).get("reserve")
        if reserve_data:
            is_borrowable = (
                reserve_data.get("borrowingEnabled") is True and
                reserve_data.get("isActive") is True and
                reserve_data.get("isFrozen") is False
            )
            logging.info(f"Aave backup query successful for {contract_address}")
            return is_borrowable

    except Exception as e:
        logging.warning(f"Backup endpoint also failed: {e}")

    return False

def is_borrowable_on_compound(contract_address: str) -> bool:
    """
    Checks if a token is available for borrowing on Compound V3.
    """
    # Compound V3 has a different structure, this is a simplified check
    query = f"""
    {{
        market(id: "{contract_address.lower()}") {{
            id
            isListed
            canBorrow
        }}
    }}
    """
    
    try:
        response = requests.post(
            COMPOUND_V3_SUBGRAPH_URL,
            json={'query': query},
            timeout=10
        )
        response.raise_for_status()
        data = response.json()
        
        if 'errors' in data:
            logging.warning(f"GraphQL errors in Compound query: {data['errors']}")
            return False
        
        market_data = data.get("data", {}).get("market")
        if market_data:
            is_borrowable = (
                market_data.get("isListed") is True and
                market_data.get("canBorrow") is True
            )
            logging.info(f"Compound market data for {contract_address}: {market_data}")
            return is_borrowable
            
    except requests.RequestException as e:
        logging.warning(f"Error querying Compound subgraph: {e}")
    except Exception as e:
        logging.error(f"Unexpected error checking Compound: {e}")
        
    return False

def is_major_token(contract_address: str) -> bool:
    """
    Fallback check for major tokens that are likely to be borrowable on Aave V3.
    This is an expanded whitelist of well-known token addresses that are typically available.
    Updated based on current Aave V3 markets.
    """
    major_tokens = {
        # Core DeFi tokens
        "******************************************": "UNI",
        "******************************************": "AAVE",
        "******************************************": "DAI",
        "******************************************": "USDC",
        "******************************************": "USDT",
        "******************************************": "WBTC",
        "******************************************": "WETH",
        "******************************************": "LINK",
        "******************************************": "MKR",

        # Additional major tokens commonly on Aave
        "******************************************": "COMP",
        "0x92d6c1e31e14519d225d5829cf70af773944c7f": "DYDX",
        "******************************************": "CVX",
        "******************************************": "CRV",
        "******************************************": "DPI",
        "******************************************": "FRAX",
        "******************************************": "FEI",
        "******************************************": "YFI",
        "******************************************": "REP",
        "******************************************": "ZRX",

        # Stablecoins
        "******************************************": "USDP",
        "******************************************": "sUSD",
        "******************************************": "TUSD",

        # Liquid staking tokens
        "******************************************": "stETH",
        "******************************************": "wstETH",
        "******************************************": "cbETH",
        "******************************************": "sFRAX"
    }

    return contract_address.lower() in major_tokens

def get_lending_protocols_info(contract_address: str) -> Dict[str, Any]:
    """
    Get detailed information about token availability across lending protocols
    """
    info = {
        "aave_v3": {"available": False, "borrowing_enabled": False},
        "compound_v3": {"available": False, "borrowing_enabled": False},
        "is_major_token": is_major_token(contract_address)
    }
    
    # Check Aave
    try:
        if is_borrowable_on_aave(contract_address):
            info["aave_v3"] = {"available": True, "borrowing_enabled": True}
    except Exception as e:
        logging.error(f"Error checking Aave for {contract_address}: {e}")
    
    # Check Compound
    try:
        if is_borrowable_on_compound(contract_address):
            info["compound_v3"] = {"available": True, "borrowing_enabled": True}
    except Exception as e:
        logging.error(f"Error checking Compound for {contract_address}: {e}")
    
    return info
