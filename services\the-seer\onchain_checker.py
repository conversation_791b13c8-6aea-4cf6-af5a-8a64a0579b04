import requests
import logging
from typing import Optional, Dict, Any

# Using the public, free Aave V3 Subgraph on Ethereum Mainnet
AAVE_V3_SUBGRAPH_URL = "https://api.thegraph.com/subgraphs/name/aave/protocol-v3"
COMPOUND_V3_SUBGRAPH_URL = "https://api.thegraph.com/subgraphs/name/graphprotocol/compound-v3"

def is_token_borrowable(contract_address: str) -> bool:
    """
    Checks if a token is available for borrowing on major lending protocols.
    Returns True if the token is listed and borrowing is enabled on any protocol.
    """
    if not contract_address:
        return False
    
    # Check Aave V3
    if is_borrowable_on_aave(contract_address):
        logging.info(f"Token {contract_address} is borrowable on Aave V3")
        return True
    
    # Check Compound V3 (if <PERSON><PERSON> fails)
    if is_borrowable_on_compound(contract_address):
        logging.info(f"Token {contract_address} is borrowable on Compound V3")
        return True
    
    # Check other protocols or use fallback logic
    if is_major_token(contract_address):
        logging.info(f"Token {contract_address} is a major token, likely borrowable")
        return True
    
    logging.info(f"Token {contract_address} is not borrowable on checked protocols")
    return False

def is_borrowable_on_aave(contract_address: str) -> bool:
    """
    Checks if a token is available for borrowing on Aave V3 by querying its subgraph.
    Returns True if the token is listed and borrowing is enabled.
    """
    # GraphQL query to check for the existence and status of a reserve
    query = f"""
    {{
        reserve(id: "{contract_address.lower()}0xb53c1a33016b2dc2ff3653530bff1848a515c8c5") {{
            id
            borrowingEnabled
            isActive
            isFrozen
            symbol
        }}
    }}
    """
    
    try:
        response = requests.post(
            AAVE_V3_SUBGRAPH_URL, 
            json={'query': query},
            timeout=10
        )
        response.raise_for_status()
        data = response.json()
        
        if 'errors' in data:
            logging.warning(f"GraphQL errors in Aave query: {data['errors']}")
            return False
        
        reserve_data = data.get("data", {}).get("reserve")
        if reserve_data:
            is_borrowable = (
                reserve_data.get("borrowingEnabled") is True and
                reserve_data.get("isActive") is True and
                reserve_data.get("isFrozen") is False
            )
            logging.info(f"Aave reserve data for {contract_address}: {reserve_data}")
            return is_borrowable
            
    except requests.RequestException as e:
        logging.warning(f"Error querying Aave subgraph: {e}")
    except Exception as e:
        logging.error(f"Unexpected error checking Aave: {e}")
        
    return False

def is_borrowable_on_compound(contract_address: str) -> bool:
    """
    Checks if a token is available for borrowing on Compound V3.
    """
    # Compound V3 has a different structure, this is a simplified check
    query = f"""
    {{
        market(id: "{contract_address.lower()}") {{
            id
            isListed
            canBorrow
        }}
    }}
    """
    
    try:
        response = requests.post(
            COMPOUND_V3_SUBGRAPH_URL,
            json={'query': query},
            timeout=10
        )
        response.raise_for_status()
        data = response.json()
        
        if 'errors' in data:
            logging.warning(f"GraphQL errors in Compound query: {data['errors']}")
            return False
        
        market_data = data.get("data", {}).get("market")
        if market_data:
            is_borrowable = (
                market_data.get("isListed") is True and
                market_data.get("canBorrow") is True
            )
            logging.info(f"Compound market data for {contract_address}: {market_data}")
            return is_borrowable
            
    except requests.RequestException as e:
        logging.warning(f"Error querying Compound subgraph: {e}")
    except Exception as e:
        logging.error(f"Unexpected error checking Compound: {e}")
        
    return False

def is_major_token(contract_address: str) -> bool:
    """
    Fallback check for major tokens that are likely to be borrowable.
    This is a whitelist of well-known token addresses.
    """
    major_tokens = {
        "******************************************": "UNI",
        "******************************************": "AAVE", 
        "******************************************": "DAI",
        "******************************************": "USDC",
        "******************************************": "USDT",
        "******************************************": "WBTC",
        "******************************************": "WETH",
        "******************************************": "LINK",
        "******************************************": "MKR",
        "******************************************": "YFI",
        "******************************************": "REP",
        "******************************************": "ZRX"
    }
    
    return contract_address.lower() in major_tokens

def get_lending_protocols_info(contract_address: str) -> Dict[str, Any]:
    """
    Get detailed information about token availability across lending protocols
    """
    info = {
        "aave_v3": {"available": False, "borrowing_enabled": False},
        "compound_v3": {"available": False, "borrowing_enabled": False},
        "is_major_token": is_major_token(contract_address)
    }
    
    # Check Aave
    try:
        if is_borrowable_on_aave(contract_address):
            info["aave_v3"] = {"available": True, "borrowing_enabled": True}
    except Exception as e:
        logging.error(f"Error checking Aave for {contract_address}: {e}")
    
    # Check Compound
    try:
        if is_borrowable_on_compound(contract_address):
            info["compound_v3"] = {"available": True, "borrowing_enabled": True}
    except Exception as e:
        logging.error(f"Error checking Compound for {contract_address}: {e}")
    
    return info
