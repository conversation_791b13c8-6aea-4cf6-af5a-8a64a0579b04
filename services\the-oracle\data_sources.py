import os
import requests
from typing import Dict, Any, List

# API Keys for data sources
# Note: Both TokenUnlocks.com and Vestlab.io are no longer operational
# TokenUnlocks.com - Domain for sale
# Vestlab.io - Domain parked
#
# Working alternatives:
COINGECKO_API_KEY = os.environ.get("COINGECKO_API_KEY")  # Free tier available
DEFILLAMA_API_KEY = os.environ.get("DEFILLAMA_API_KEY")  # Free, no key required
MESSARI_API_KEY = os.environ.get("MESSARI_API_KEY")      # Free tier available

# Legacy (deprecated) - kept for backward compatibility
TOKENUNLOCKS_API_KEY = os.environ.get("TOKENUNLOCKS_API_KEY")  # DEPRECATED

def fetch_token_unlocks_data() -> List[Dict[str, Any]]:
    """
    Fetches upcoming token unlock data from available sources.

    Since TokenUnlocks.com and Vestlab.io are no longer operational,
    this function now uses:
    1. Mock data for testing
    2. CoinGecko API for basic token data
    3. DefiLlama for protocol data
    4. Manual curated unlock events

    In production, you would implement:
    - On-chain vesting contract parsing
    - Social media monitoring for unlock announcements
    - Protocol documentation scraping
    """

    # Try to fetch from working APIs first
    events = []

    # 1. Try CoinGecko for basic token data
    coingecko_events = fetch_from_coingecko_api()
    events.extend(coingecko_events)

    # 2. Try DefiLlama for protocol data
    defillama_events = fetch_from_defillama_api()
    events.extend(defillama_events)

    # 3. Fallback to curated mock data for testing
    if not events:
        print("INFO: Using curated test data (no live APIs available)")
        events = get_curated_unlock_events()

    return events

def get_curated_unlock_events() -> List[Dict[str, Any]]:
    """
    Curated list of known unlock events for testing.
    In production, this would be replaced with real data sources.
    """
    mock_events = [
        {
            "token_symbol": "DYDX",
            "contract_address": "0x92D6C1e31e14519D225d5829CF70AF773944c7f",
            "unlock_date": "2024-12-01T00:00:00Z",
            "unlock_amount": 150000000.0,
            "circulating_supply": 300000000.0,
            "total_supply": 1000000000.0,
            "source": "TokenUnlocks.com"
        },
        {
            "token_symbol": "UNI",
            "contract_address": "0x1f9840a85d5aF5bf1D1762F925BDADdC4201F984",
            "unlock_date": "2024-11-15T00:00:00Z",
            "unlock_amount": 83333333.0,
            "circulating_supply": 750000000.0,
            "total_supply": 1000000000.0,
            "source": "TokenUnlocks.com"
        },
        {
            "token_symbol": "AAVE",
            "contract_address": "0x7Fc66500c84A76Ad7e9c93437bFc5Ac33E2DDaE9",
            "unlock_date": "2024-11-20T00:00:00Z",
            "unlock_amount": 25000000.0,
            "circulating_supply": 14000000.0,
            "total_supply": 16000000.0,
            "source": "TokenUnlocks.com"
        }
    ]
    
    # In a real implementation:
    # response = requests.get("https://api.tokenunlocks.com/v1/events", headers={"X-API-KEY": TOKENUNLOCKS_API_KEY})
    # normalized_data = normalize(response.json())
    return mock_events

def fetch_from_tokenunlocks_api() -> List[Dict[str, Any]]:
    """
    Fetches data from TokenUnlocks.com API
    """
    if not TOKENUNLOCKS_API_KEY:
        print("WARNING: TOKENUNLOCKS_API_KEY not set. Using mock data.")
        return []
    
    try:
        # This would be the actual API call
        # response = requests.get(
        #     "https://api.tokenunlocks.com/v1/events",
        #     headers={"X-API-KEY": TOKENUNLOCKS_API_KEY},
        #     params={"days_ahead": 30}
        # )
        # response.raise_for_status()
        # return normalize_tokenunlocks_data(response.json())
        return []
    except requests.RequestException as e:
        print(f"Error fetching from TokenUnlocks API: {e}")
        return []

def fetch_from_coingecko_api() -> List[Dict[str, Any]]:
    """
    Fetches token data from CoinGecko API (free tier available)
    While CoinGecko doesn't have unlock data, it provides market data
    that can be combined with manual unlock tracking.
    """
    try:
        # Get trending tokens that might have upcoming unlocks
        url = "https://api.coingecko.com/api/v3/search/trending"
        headers = {}
        if COINGECKO_API_KEY:
            headers["x-cg-demo-api-key"] = COINGECKO_API_KEY

        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        # This is just an example - you'd need to cross-reference with unlock data
        print("INFO: CoinGecko API accessible, but no unlock data available")
        return []

    except requests.RequestException as e:
        print(f"INFO: CoinGecko API not available: {e}")
        return []

def fetch_from_defillama_api() -> List[Dict[str, Any]]:
    """
    Fetches protocol data from DefiLlama API (free, no key required)
    Can provide TVL and protocol data to cross-reference with unlock events.
    """
    try:
        # Get protocol data
        url = "https://api.llama.fi/protocols"
        response = requests.get(url, timeout=10)
        response.raise_for_status()

        # This is just an example - you'd need to cross-reference with unlock data
        print("INFO: DefiLlama API accessible, but no unlock data available")
        return []

    except requests.RequestException as e:
        print(f"INFO: DefiLlama API not available: {e}")
        return []

def fetch_from_deprecated_apis() -> List[Dict[str, Any]]:
    """
    Legacy function for deprecated APIs (TokenUnlocks, Vestlab)
    Kept for backward compatibility but returns empty list.
    """
    print("WARNING: TokenUnlocks.com and Vestlab.io are no longer operational")
    print("INFO: Using alternative data sources and curated events")
    return []
